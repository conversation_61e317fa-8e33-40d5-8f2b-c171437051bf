#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化提取脚本 - 专注提取效果不好的关键指标
主要针对：血脂四项、血糖、吸烟饮酒状况
"""

import os
import re
import sys
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any
import fitz  # PyMuPDF

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedExtractor:
    """优化的医学指标提取器"""
    
    def __init__(self):
        self.results = []
        
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """从PDF提取文本"""
        try:
            doc = fitz.open(pdf_path)
            full_text = ""
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                full_text += text + "\n"
            
            doc.close()
            return full_text
            
        except Exception as e:
            logger.error(f"PDF文本提取失败 {pdf_path}: {e}")
            return ""
    
    def extract_name(self, text: str) -> str:
        """提取姓名"""
        # 多种姓名格式
        name_patterns = [
            r'姓名[:\s]*([^\s\n]{2,4})',
            r'患者姓名[:\s]*([^\s\n]{2,4})',
            r'病人姓名[:\s]*([^\s\n]{2,4})',
            r'受检者[:\s]*([^\s\n]{2,4})',
            r'姓\s*名[:\s]*([^\s\n]{2,4})'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, text)
            if match:
                name = match.group(1).strip()
                # 过滤无效姓名
                if len(name) >= 2 and not re.match(r'^\d+$', name):
                    return name
        
        return ""
    
    def extract_lipid_values(self, text: str) -> Dict[str, str]:
        """提取血脂四项 - 优化版本"""
        results = {}
        
        # 血脂四项关键词映射
        lipid_keywords = {
            '总胆固醇': ['总胆固醇', 'TC', 'CHOL', '胆固醇总量'],
            '甘油三酯': ['甘油三酯', 'TG', 'TRIG', '三酰甘油'],
            '高密度脂蛋白胆固醇': ['高密度脂蛋白胆固醇', 'HDL-C', 'HDL', '高密度脂蛋白'],
            '低密度脂蛋白胆固醇': ['低密度脂蛋白胆固醇', 'LDL-C', 'LDL', '低密度脂蛋白']
        }
        
        for field_name, keywords in lipid_keywords.items():
            value = self._extract_value_with_keywords(text, keywords, field_name)
            results[field_name] = value
            
        return results
    
    def extract_glucose(self, text: str) -> str:
        """提取血糖值 - 关键词葡萄糖"""
        keywords = ['葡萄糖', 'GLU', 'GLUC', '血糖']
        return self._extract_value_with_keywords(text, keywords, '葡萄糖')

    def extract_hba1c(self, text: str) -> str:
        """提取糖化血红蛋白值"""
        # 糖化血红蛋白的多种表达方式
        hba1c_patterns = [
            r'糖化血红蛋白\[HPLC法\]\s*(\d+(?:\.\d+)?)',  # 糖化血红蛋白[HPLC法]5.6
            r'糖化血红蛋白\[[^\]]*\]\s*(\d+(?:\.\d+)?)',  # 糖化血红蛋白[任何方法]数值
            r'糖化血红蛋白\s*\([^)]*\)\s*(\d+(?:\.\d+)?)',  # 糖化血红蛋白(方法)数值
            r'糖化血红蛋白[^0-9]*?(\d+(?:\.\d+)?)\s*%',  # 糖化血红蛋白...数值%
            r'糖化血红蛋白[:\s]*(\d+(?:\.\d+)?)',  # 糖化血红蛋白:数值
            r'HbA1c\s*\[[^\]]*\]\s*(\d+(?:\.\d+)?)',  # HbA1c[方法]数值
            r'HbA1c\s*\([^)]*\)\s*(\d+(?:\.\d+)?)',  # HbA1c(方法)数值
            r'HbA1c[:\s]*(\d+(?:\.\d+)?)',  # HbA1c:数值
            r'糖化血红蛋白\s+(\d+(?:\.\d+)?)\s+[\d\.-]+\s*%',  # 表格格式：糖化血红蛋白 数值 参考范围 %
        ]

        for pattern in hba1c_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    value = float(match.group(1))
                    # 糖化血红蛋白合理范围 4.0-15.0%
                    if 4.0 <= value <= 15.0:
                        logger.debug(f"糖化血红蛋白 找到值: {match.group(1)} (模式: {pattern})")
                        return match.group(1)
                except ValueError:
                    continue

        logger.debug("糖化血红蛋白 未找到有效值")
        return ""
    
    def _extract_value_with_keywords(self, text: str, keywords: List[str], field_name: str) -> str:
        """通用的数值提取方法 - 优化版本"""
        
        # 为每个关键词尝试提取
        all_matches = []
        
        for keyword in keywords:
            # 构建更灵活的正则表达式
            patterns = [
                # 标准格式：关键词 数值 单位
                rf'{re.escape(keyword)}\s*[:\(\)\[\]]*\s*(\d+(?:\.\d+)?)\s*[a-zA-Z/]*',
                # 带括号的格式：关键词(英文) 数值
                rf'{re.escape(keyword)}\s*\([^)]*\)\s*[:\s]*(\d+(?:\.\d+)?)',
                # 表格格式：关键词 ... 数值 参考范围
                rf'{re.escape(keyword)}[^0-9]*?(\d+(?:\.\d+)?)\s+[\d\.-]+\s*[a-zA-Z/]*',
                # 简单格式：关键词:数值
                rf'{re.escape(keyword)}[:\s]*(\d+(?:\.\d+)?)',
                # 复杂格式：关键词 其他字符 数值
                rf'{re.escape(keyword)}[^0-9]*?(\d+(?:\.\d+)?)'
            ]
            
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    try:
                        value = float(match.group(1))
                        # 根据不同指标设置合理范围
                        if self._is_valid_value(field_name, value):
                            all_matches.append({
                                'value': match.group(1),
                                'position': match.start(),
                                'keyword': keyword,
                                'pattern': pattern
                            })
                    except ValueError:
                        continue
        
        if all_matches:
            # 选择最佳匹配（优先选择位置靠后的，可能是检测结果）
            best_match = max(all_matches, key=lambda x: x['position'])
            logger.debug(f"{field_name} 找到值: {best_match['value']} (关键词: {best_match['keyword']})")
            return best_match['value']
        
        logger.debug(f"{field_name} 未找到有效值")
        return ""
    
    def _is_valid_value(self, field_name: str, value: float) -> bool:
        """验证数值是否在合理范围内"""
        ranges = {
            '总胆固醇': (1.0, 15.0),      # mmol/L
            '甘油三酯': (0.1, 10.0),      # mmol/L
            '高密度脂蛋白胆固醇': (0.5, 3.0),  # mmol/L
            '低密度脂蛋白胆固醇': (0.5, 8.0),  # mmol/L
            '葡萄糖': (2.0, 30.0),        # mmol/L
            '糖化血红蛋白': (4.0, 15.0)   # %
        }

        if field_name in ranges:
            min_val, max_val = ranges[field_name]
            return min_val <= value <= max_val

        return True  # 默认接受
    
    def extract_lifestyle_info(self, text: str) -> Dict[str, str]:
        """提取吸烟饮酒状况 - 优化版本"""
        results = {}

        # 吸烟状况 - 更精确的模式
        smoking_patterns = [
            r'吸烟状况[:\s]*([不从未]?[吸抽]?烟?[^\n\r，。]{0,10})',
            r'吸烟[:\s]*([不从未]?[吸抽]?烟?[^\n\r，。]{0,10})',
            r'烟草使用[:\s]*([不从未]?[使用]?[^\n\r，。]{0,10})',
            r'吸烟史[:\s]*([不从未]?[吸抽]?烟?[^\n\r，。]{0,10})'
        ]

        for pattern in smoking_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                smoking_info = match.group(1).strip()
                # 清理无关字符和标点
                smoking_info = re.sub(r'[:\s，。、]+$', '', smoking_info)
                smoking_info = re.sub(r'^[:\s，。、]+', '', smoking_info)

                # 验证是否是有效的吸烟状况描述
                if self._is_valid_lifestyle_info(smoking_info, 'smoking'):
                    results['吸烟状况'] = smoking_info
                    break

        # 饮酒状况 - 更精确的模式
        drinking_patterns = [
            r'饮酒状况[:\s]*([不从未]?[饮喝]?酒?[^\n\r，。]{0,10})',
            r'饮酒[:\s]*([不从未]?[饮喝]?酒?[^\n\r，。]{0,10})',
            r'酒精使用[:\s]*([不从未]?[使用]?[^\n\r，。]{0,10})',
            r'饮酒史[:\s]*([不从未]?[饮喝]?酒?[^\n\r，。]{0,10})'
        ]

        for pattern in drinking_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                drinking_info = match.group(1).strip()
                # 清理无关字符和标点
                drinking_info = re.sub(r'[:\s，。、]+$', '', drinking_info)
                drinking_info = re.sub(r'^[:\s，。、]+', '', drinking_info)

                # 验证是否是有效的饮酒状况描述
                if self._is_valid_lifestyle_info(drinking_info, 'drinking'):
                    results['饮酒状况'] = drinking_info
                    break

        return results

    def _is_valid_lifestyle_info(self, info: str, info_type: str) -> bool:
        """验证生活习惯信息是否有效"""
        if not info or len(info) > 20:  # 长度限制
            return False

        # 排除明显错误的内容
        invalid_patterns = [
            r'[0-9]{4,}',  # 长数字
            r'[a-zA-Z]{3,}',  # 长英文
            r'[^\u4e00-\u9fa5\d\s不从未吸抽烟饮喝酒偶尔经常少量适量大量戒]{3,}',  # 非中文字符过多
            r'疼痛|黄疸|发热|症状|医院|检查|治疗|建议'  # 医学术语
        ]

        for pattern in invalid_patterns:
            if re.search(pattern, info):
                return False

        # 验证是否包含相关关键词
        if info_type == 'smoking':
            valid_keywords = ['不吸烟', '从不吸烟', '未吸烟', '吸烟', '抽烟', '戒烟', '偶尔', '经常']
        else:  # drinking
            valid_keywords = ['不饮酒', '从不饮酒', '未饮酒', '饮酒', '喝酒', '戒酒', '偶尔', '经常', '少量', '适量']

        return any(keyword in info for keyword in valid_keywords)

    def extract_medical_history(self, text: str) -> Dict[str, str]:
        """提取医学史信息 - 既往史、家族史、过敏史、现病史"""
        results = {}

        # 医学史字段映射
        history_fields = {
            '既往史': ['既往史', '既往病史', '过去史', '病史'],
            '家族史': ['家族史', '家庭史', '遗传史'],
            '过敏史': ['过敏史', '药物过敏史', '食物过敏史', '过敏反应史'],
            '现病史': ['现病史', '现在病史', '本次病史', '主诉']
        }

        for field_name, keywords in history_fields.items():
            value = self._extract_history_with_keywords(text, keywords, field_name)
            if value:
                results[field_name] = value

        return results

    def _extract_history_with_keywords(self, text: str, keywords: List[str], field_name: str) -> str:
        """提取医学史信息的通用方法 - 优化版本"""

        for keyword in keywords:
            # 构建医学史提取的正则表达式 - 针对表格格式优化
            patterns = [
                # 表格格式：关键词 空格 内容（最常见）
                rf'{re.escape(keyword)}\s+([^\s\n\r]{{1,50}})(?:\s|$)',
                # 标准格式：关键词: 内容
                rf'{re.escape(keyword)}[:\s]*([^\n\r。；;]{{1,50}})',
                # 带冒号格式：关键词：内容
                rf'{re.escape(keyword)}：([^\n\r。；;]{{1,50}})',
                # 紧密格式：关键词直接跟内容
                rf'{re.escape(keyword)}([无有否认未见正常异常][^\s\n\r]{{0,30}})',
            ]

            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    history_info = match.group(1).strip()

                    # 清理和验证提取的内容
                    history_info = self._clean_history_info(history_info)

                    # 验证是否是有效的医学史信息
                    if self._is_valid_history_info(history_info, field_name):
                        logger.debug(f"{field_name} 找到信息: {history_info}")
                        return history_info

        logger.debug(f"{field_name} 未找到有效信息")
        return ""

    def _clean_history_info(self, info: str) -> str:
        """清理医学史信息"""
        if not info:
            return ""

        # 移除开头和结尾的标点符号和空格
        info = re.sub(r'^[:\s，。、；;]+', '', info)
        info = re.sub(r'[:\s，。、；;]+$', '', info)

        # 移除多余的空格
        info = re.sub(r'\s+', ' ', info)

        return info.strip()

    def _is_valid_history_info(self, info: str, field_name: str) -> bool:
        """验证医学史信息是否有效 - 优化版本"""
        if not info or len(info) < 1:
            return False

        # 长度限制 - 医学史通常比较简短
        if len(info) > 100:
            return False

        # 排除明显无效的内容
        invalid_patterns = [
            r'^[0-9\.\-\s]+$',  # 纯数字
            r'^[a-zA-Z\s]+$',   # 纯英文（除非是常见医学术语）
            r'^\W+$',           # 纯符号
            r'^[/\s]+$',        # 只有斜杠和空格
        ]

        for pattern in invalid_patterns:
            if re.match(pattern, info):
                return False

        # 检查是否包含常见的医学史相关词汇
        valid_indicators = [
            # 常见否定词
            '无', '否认', '未见', '未发现', '无异常', '无特殊', '无明显',
            # 常见肯定词
            '有', '患', '病', '史', '正常', '异常',
            # 时间词
            '年', '月', '日', '前', '后',
            # 疾病相关
            '高血压', '糖尿病', '心脏病', '手术', '外伤', '药物', '食物', '过敏',
            # 家族相关
            '家族', '父', '母', '兄', '弟', '姐', '妹', '子', '女', '遗传', '家庭',
            # 其他医学术语
            '治疗', '住院', '检查', '诊断', '服药', '手术史', '外伤史'
        ]

        # 至少包含一个有效指示词，或者是单字符的常见医学史描述
        has_valid_indicator = any(indicator in info for indicator in valid_indicators)
        is_single_char_valid = len(info) <= 3 and info in ['无', '有', '否', '/', '未见', '正常', '异常']

        # 调试信息
        if info:
            logger.debug(f"验证医学史信息: '{info}' - 有效指示词: {has_valid_indicator}, 单字符有效: {is_single_char_valid}")

        return has_valid_indicator or is_single_char_valid
    
    def process_single_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """处理单个PDF文件"""
        logger.info(f"处理文件: {os.path.basename(pdf_path)}")
        
        # 提取文本
        text = self.extract_text_from_pdf(pdf_path)
        if not text:
            logger.warning(f"无法提取文本: {pdf_path}")
            return {}
        
        # 初始化结果
        result = {
            '文件名': os.path.basename(pdf_path),
            '姓名': '',
            '总胆固醇': '',
            '甘油三酯': '',
            '高密度脂蛋白胆固醇': '',
            '低密度脂蛋白胆固醇': '',
            '葡萄糖': '',
            '糖化血红蛋白': '',
            '吸烟状况': '',
            '饮酒状况': '',
            '既往史': '',
            '家族史': '',
            '过敏史': '',
            '现病史': ''
        }
        
        # 提取各项指标
        result['姓名'] = self.extract_name(text)

        # 血脂四项
        lipid_values = self.extract_lipid_values(text)
        result.update(lipid_values)

        # 血糖
        result['葡萄糖'] = self.extract_glucose(text)

        # 糖化血红蛋白
        result['糖化血红蛋白'] = self.extract_hba1c(text)

        # 生活习惯
        lifestyle_info = self.extract_lifestyle_info(text)
        result.update(lifestyle_info)

        # 医学史信息
        medical_history = self.extract_medical_history(text)
        result.update(medical_history)
        
        # 记录提取结果
        extracted_fields = [k for k, v in result.items() if v and k != '文件名']
        logger.info(f"  提取到字段: {extracted_fields}")
        
        return result
    
    def process_directory(self, pdf_dir: str) -> List[Dict[str, Any]]:
        """处理目录下的所有PDF文件"""
        pdf_dir = Path(pdf_dir)
        if not pdf_dir.exists():
            logger.error(f"目录不存在: {pdf_dir}")
            return []
        
        pdf_files = list(pdf_dir.glob("*.pdf"))
        logger.info(f"找到 {len(pdf_files)} 个PDF文件")
        
        results = []
        for pdf_file in pdf_files:
            try:
                result = self.process_single_pdf(str(pdf_file))
                if result:
                    results.append(result)
            except Exception as e:
                logger.error(f"处理文件失败 {pdf_file}: {e}")
                continue
        
        return results
    
    def save_to_excel(self, results: List[Dict[str, Any]], output_path: str):
        """保存结果到Excel"""
        if not results:
            logger.warning("没有结果可保存")
            return
        
        df = pd.DataFrame(results)
        
        # 确保输出目录存在
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存到Excel
        df.to_excel(output_path, index=False)
        logger.info(f"结果已保存到: {output_path}")
        
        # 打印统计信息
        self.print_statistics(df)
    
    def print_statistics(self, df: pd.DataFrame):
        """打印提取统计信息"""
        total_count = len(df)
        logger.info(f"\n=== 提取统计 ===")
        logger.info(f"总文件数: {total_count}")
        
        fields = ['姓名', '总胆固醇', '甘油三酯', '高密度脂蛋白胆固醇', '低密度脂蛋白胆固醇', '葡萄糖', '糖化血红蛋白', '吸烟状况', '饮酒状况', '既往史', '家族史', '过敏史', '现病史']
        
        for field in fields:
            if field in df.columns:
                success_count = df[field].notna().sum() - (df[field] == '').sum()
                success_rate = success_count / total_count * 100
                logger.info(f"{field}: {success_count}/{total_count} ({success_rate:.1f}%)")

def main():
    """主函数"""
    # 设置路径
    pdf_directory = "./files/7.28体检第三批"
    output_file = "./output/optimized_extraction_results_enhanced3.xlsx"
    
    # 创建提取器
    extractor = OptimizedExtractor()
    
    # 处理所有PDF文件
    logger.info("开始优化提取任务...")
    results = extractor.process_directory(pdf_directory)
    
    if results:
        # 保存结果
        extractor.save_to_excel(results, output_file)
        logger.info("优化提取任务完成!")
    else:
        logger.error("没有成功提取任何数据")

if __name__ == "__main__":
    main()
