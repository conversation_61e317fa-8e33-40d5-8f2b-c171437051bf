#!/usr/bin/env python3
"""
Main Execution Script for Holter OCR System
Orchestrates the entire OCR extraction process with command-line interface
"""

import os
import sys
import argparse
import json
from datetime import datetime
from typing import Dict, Any, Optional

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ocr_extractor import HolterOCRExtractor
from field_extractor import HolterFieldExtractor
from batch_processor import HolterBatchProcessor
from data_exporter import HolterDataExporter
from error_handler import setup_global_error_handler, HolterErrorHandler

os.environ["KMP_DUPLICATE_LIB_OK"]="TRUE"

class HolterOCRSystem:
    """Main orchestrator for the Holter OCR system"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Holter OCR system
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.error_handler = setup_global_error_handler(
            log_dir=config.get('log_dir', 'logs'),
            log_level=config.get('log_level', 'INFO')
        )
        
        # Initialize components
        self.ocr_extractor = HolterOCRExtractor(
            use_gpu=config.get('use_gpu', False),
            lang=config.get('ocr_language', 'ch')
        )
        
        self.field_extractor = HolterFieldExtractor()
        
        self.batch_processor = HolterBatchProcessor(
            pdf_directory=config['pdf_directory'],
            output_file=config.get('output_file', 'holter_extracted_data.xlsx'),
            max_workers=config.get('max_workers', 4)
        )
        
        self.data_exporter = HolterDataExporter(
            template_file=config.get('template_file')
        )
        
        self.logger = self.error_handler.logger
    
    def validate_config(self) -> bool:
        """
        Validate configuration parameters
        
        Returns:
            True if configuration is valid
        """
        required_fields = ['pdf_directory']
        
        for field in required_fields:
            if field not in self.config:
                self.logger.error(f"Missing required configuration field: {field}")
                return False
        
        # Check if PDF directory exists
        if not os.path.exists(self.config['pdf_directory']):
            self.logger.error(f"PDF directory not found: {self.config['pdf_directory']}")
            return False
        
        # Check if there are PDF files
        pdf_files = [f for f in os.listdir(self.config['pdf_directory']) 
                    if f.lower().endswith('.pdf')]
        if not pdf_files:
            self.logger.error(f"No PDF files found in: {self.config['pdf_directory']}")
            return False
        
        self.logger.info(f"Configuration validated - Found {len(pdf_files)} PDF files")
        return True
    
    def run_extraction(self) -> Optional[str]:
        """
        Run the complete extraction process
        
        Returns:
            Path to output file if successful, None otherwise
        """
        try:
            self.logger.info("Starting Holter OCR extraction process")
            
            # Validate configuration
            if not self.validate_config():
                return None
            
            # Run batch processing
            output_file = self.batch_processor.run_batch_processing(
                use_threading=self.config.get('use_threading', True),
                save_excel=True
            )
            
            if output_file and os.path.exists(output_file):
                self.logger.info(f"Extraction completed successfully: {output_file}")
                
                # Generate summary report if requested
                if self.config.get('generate_summary', False):
                    summary_file = output_file.replace('.xlsx', '_summary.xlsx')
                    try:
                        # Load the data and create summary
                        import pandas as pd
                        df = pd.read_excel(output_file)
                        data_list = df.to_dict('records')
                        
                        self.data_exporter.export_summary_report(data_list, summary_file)
                        self.logger.info(f"Summary report created: {summary_file}")
                    except Exception as e:
                        self.logger.warning(f"Failed to create summary report: {e}")
                
                return output_file
            else:
                self.logger.error("Extraction failed - no output file created")
                return None
                
        except Exception as e:
            self.error_handler.log_error(e, {'operation': 'main_extraction'})
            return None
        finally:
            # Save error report
            self.error_handler.save_error_report()
    
    def run_single_file_test(self, pdf_path: str) -> Dict[str, Any]:
        """
        Test extraction on a single PDF file
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            Extraction results
        """
        try:
            self.logger.info(f"Testing extraction on: {pdf_path}")
            
            # Extract text using OCR
            ocr_text = self.ocr_extractor.get_full_text(pdf_path)
            
            if not ocr_text.strip():
                return {'error': 'No text extracted from PDF'}
            
            # Extract fields
            filename = os.path.basename(pdf_path)
            extracted_data = self.field_extractor.extract_all_fields(ocr_text, filename)
            cleaned_data = self.field_extractor.clean_and_validate_data(extracted_data)
            
            return {
                'success': True,
                'filename': filename,
                'ocr_text_length': len(ocr_text),
                'extracted_fields': len([v for v in cleaned_data.values() if v is not None]),
                'data': cleaned_data
            }
            
        except Exception as e:
            self.error_handler.handle_pdf_error(pdf_path, e, 'single_file_test')
            return {'error': str(e)}


def load_config(config_file: str = None) -> Dict[str, Any]:
    """
    Load configuration from file or create default
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        Configuration dictionary
    """
    default_config = {
        'pdf_directory': 'Holter',
        'output_file': 'holter_extracted_data.xlsx',
        'template_file': '安医附院Holter（2024）.xlsx',
        'log_dir': 'logs',
        'log_level': 'INFO',
        'use_gpu': False,
        'ocr_language': 'ch',
        'max_workers': 4,
        'use_threading': True,
        'generate_summary': True
    }
    
    if config_file and os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            default_config.update(file_config)
            print(f"Configuration loaded from: {config_file}")
        except Exception as e:
            print(f"Error loading config file: {e}")
            print("Using default configuration")
    
    return default_config


def create_sample_config(output_file: str = 'holter_config.json'):
    """Create a sample configuration file"""
    config = load_config()
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"Sample configuration created: {output_file}")
    except Exception as e:
        print(f"Error creating config file: {e}")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='Holter OCR System - Extract medical data from PDF reports',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                          # Run with default settings
  python main.py --config my_config.json # Run with custom config
  python main.py --test test.pdf          # Test single file
  python main.py --create-config          # Create sample config file
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='Configuration file path (JSON format)'
    )
    
    parser.add_argument(
        '--test', '-t',
        type=str,
        help='Test extraction on a single PDF file'
    )
    
    parser.add_argument(
        '--create-config',
        action='store_true',
        help='Create a sample configuration file'
    )
    
    parser.add_argument(
        '--pdf-dir',
        type=str,
        help='PDF directory path (overrides config)'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        help='Output Excel file path (overrides config)'
    )
    
    parser.add_argument(
        '--workers',
        type=int,
        help='Number of worker threads (overrides config)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Handle create-config command
    if args.create_config:
        create_sample_config()
        return
    
    # Load configuration
    config = load_config(args.config)
    
    # Override config with command line arguments
    if args.pdf_dir:
        config['pdf_directory'] = args.pdf_dir
    if args.output:
        config['output_file'] = args.output
    if args.workers:
        config['max_workers'] = args.workers
    if args.verbose:
        config['log_level'] = 'DEBUG'
    
    # Initialize system
    system = HolterOCRSystem(config)
    
    # Handle test command
    if args.test:
        if not os.path.exists(args.test):
            print(f"Test file not found: {args.test}")
            return
        
        result = system.run_single_file_test(args.test)
        print("\nTest Results:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return
    
    # Run main extraction
    print("Starting Holter OCR extraction...")
    print(f"PDF Directory: {config['pdf_directory']}")
    print(f"Output File: {config['output_file']}")
    print(f"Workers: {config['max_workers']}")
    print("-" * 50)
    
    output_file = system.run_extraction()
    
    if output_file:
        print(f"\nExtraction completed successfully!")
        print(f"Output file: {output_file}")
    else:
        print(f"\nExtraction failed. Check logs for details.")
        sys.exit(1)


if __name__ == "__main__":
    main()
