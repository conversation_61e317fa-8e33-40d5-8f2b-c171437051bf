"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: temp_rename_helper
date: 2025/8/14 10:22
desc: 
"""
import os
import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import PatternFill


def rename_pdfs_from_excel(excel_path, pdf_folder, output_path=None):
    """
    根据Excel文件中的信息重命名PDF文件，并标记不同状态的行

    参数:
        excel_path (str): Excel文件路径
        pdf_folder (str): 包含PDF文件的文件夹路径
        output_path (str): 可选，输出文件路径。如果为None，则在原文件名后加"_result"
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path, sheet_name='改名')

        # 检查必要的列是否存在
        if '文件名' not in df.columns or '替换名' not in df.columns:
            print("错误: Excel文件中必须包含'文件名'和'替换名'列")
            return

        # 添加状态列
        df['状态'] = ''

        # 统计不同状态的数量
        stats = {
            'success': 0,
            'exists': 0,
            'not_found': 0,
            'error': 0
        }

        # 定义颜色
        GREEN = '00FF00'  # 成功
        YELLOW = 'FFFF00'  # 文件已存在
        RED = 'FF0000'  # 失败/错误

        # 遍历Excel中的每一行
        for index, row in df.iterrows():
            original_name = str(row['文件名']).strip()
            new_name = str(row['替换名']).strip()

            # 确保文件名有.pdf后缀
            if not original_name.lower().endswith('.pdf'):
                original_name += '.pdf'
            if not new_name.lower().endswith('.pdf'):
                new_name += '.pdf'

            # 构建文件路径
            original_path = os.path.join(pdf_folder, original_name)
            new_path = os.path.join(pdf_folder, new_name)

            # 检查源文件是否存在
            if not os.path.exists(original_path):
                status = f"文件未找到: {original_name}"
                print(status)
                df.at[index, '状态'] = '失败: 源文件未找到'
                stats['not_found'] += 1
                continue

            # 检查目标文件是否已存在
            if os.path.exists(new_path):
                status = f"目标文件已存在: {new_name} (未执行重命名)"
                print(status)
                df.at[index, '状态'] = '警告: 目标文件已存在'
                stats['exists'] += 1
                continue

            # 尝试重命名文件
            try:
                os.rename(original_path, new_path)
                status = f"成功: {original_name} -> {new_name}"
                print(status)
                df.at[index, '状态'] = '成功'
                stats['success'] += 1
            except Exception as e:
                status = f"重命名失败: {original_name} -> {new_name}. 错误: {str(e)}"
                print(status)
                df.at[index, '状态'] = f'失败: {str(e)}'
                stats['error'] += 1

        # 输出统计信息
        print(f"\n操作完成统计:")
        print(f"成功: {stats['success']} 个")
        print(f"目标文件已存在: {stats['exists']} 个")
        print(f"源文件未找到: {stats['not_found']} 个")
        print(f"其他错误: {stats['error']} 个")

        # 确定输出路径
        if output_path is None:
            base, ext = os.path.splitext(excel_path)
            output_path = f"{base}_result{ext}"

        # 保存结果到新文件
        df.to_excel(output_path, sheet_name='改名', index=False)

        # 使用openpyxl设置单元格颜色
        wb = load_workbook(output_path)
        ws = wb['改名']

        # 创建填充样式
        green_fill = PatternFill(start_color=GREEN, end_color=GREEN, fill_type='solid')
        yellow_fill = PatternFill(start_color=YELLOW, end_color=YELLOW, fill_type='solid')
        red_fill = PatternFill(start_color=RED, end_color=RED, fill_type='solid')

        # 获取状态列的索引
        status_col = None
        for cell in ws[1]:  # 第一行是标题行
            if cell.value == '状态':
                status_col = cell.column_letter
                break

        if status_col:
            for row in range(2, ws.max_row + 1):
                status = ws[f"{status_col}{row}"].value
                if status == '成功':
                    fill = green_fill
                elif status.startswith('警告:'):
                    fill = yellow_fill
                else:
                    fill = red_fill

                for col in range(1, ws.max_column + 1):
                    ws.cell(row=row, column=col).fill = fill

        wb.save(output_path)
        print(f"\n结果已保存到: {output_path}")

    except Exception as e:
        print(f"发生错误: {str(e)}")


if __name__ == "__main__":
    # 配置参数
    excel_file = "./PDF提取汇总.xlsx"  # 替换为您的Excel文件路径
    pdf_directory = "./files/体检总/"  # 替换为您的PDF文件夹路径
    output_file ="./体检改名.xlsx"
    # 调用函数
    rename_pdfs_from_excel(excel_path = excel_file, pdf_folder = pdf_directory,output_path=output_file)