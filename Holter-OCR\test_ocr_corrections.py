#!/usr/bin/env python3
"""
Test script for OCR error corrections
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import sys
sys.path.append('.')

from field_extractor import HolterFieldExtractor

def test_ocr_corrections():
    """Test OCR error correction functionality"""
    
    extractor = HolterFieldExtractor()
    
    # Test cases with OCR errors
    test_cases = [
        {
            'name': 'Original OCR errors',
            'text': '''
            SONN:18lms
            1MSSD:24m5
            SDANN:163mS
            DNN50:3.24%
            pNNSO:2.1596
            ''',
            'expected_fields': ['SDNN', 'rMSSD', 'SDANN', 'pNN50']
        },
        {
            'name': 'More OCR variations',
            'text': '''
            SANN: 142ms
            IMSSD: 35m5
            SOANN: 128mS
            ONN50: 4.2%
            SDMNIndex: 45ms
            ''',
            'expected_fields': ['SDNN', 'rMSSD', 'SDANN', 'pNN50', 'SDNNIndex']
        },
        {
            'name': 'Mixed correct and incorrect',
            'text': '''
            SDNN: 120ms
            rMSSO: 28m5
            SDAMN: 115mS
            pNN5O: 3.8g6
            ''',
            'expected_fields': ['SDNN', 'rMSSD', 'SDANN', 'pNN50']
        }
    ]
    
    print("Testing OCR error corrections...")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}: {test_case['name']}")
        print("-" * 30)
        
        # Test normalization
        normalized = extractor.normalize_ocr_text(test_case['text'])
        print("Original text:", repr(test_case['text']))
        print("Normalized text:", repr(normalized))
        
        # Test field extraction
        extracted_data = extractor.extract_all_fields(test_case['text'], 'test.pdf')
        
        print("\nExtracted fields:")
        found_count = 0
        for field in test_case['expected_fields']:
            value = extracted_data.get(field)
            if value is not None:
                print(f"  ✓ {field}: {value}")
                found_count += 1
            else:
                print(f"  ✗ {field}: Not found")
        
        success_rate = found_count / len(test_case['expected_fields']) * 100
        print(f"\nSuccess rate: {found_count}/{len(test_case['expected_fields'])} ({success_rate:.1f}%)")
        print("=" * 50)

if __name__ == "__main__":
    test_ocr_corrections()
